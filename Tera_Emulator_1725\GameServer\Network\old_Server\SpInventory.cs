﻿using System.Collections.Generic;
using System.IO;
using Data.Structures.Player;

namespace Network.Server
{
    public class SpInventory : ASendPacket
    {
        protected Player Player;
        protected bool IsInventory;

        public SpInventory(Player player, bool isInventory = false)
        {
            Player = player; //73
            IsInventory = isInventory;
        }

        //C092 0C00 2700 093B0B0000800000 0000000000000000 00 0100 28000000 22000000 22000000 2700 C100 12270000 B4BA070000000000 E3290000 00000000 16000000 00000000 01000000    00000000   1E000000   00000000   00000000  00000000  00000000  00000000    00   00000000 00000000 00000000 00000000   000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 0000 03000000 00000000 C1005B01993A0000B5BA070000000000E329000000000000170000000000000001000000000000001E00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000005B01F5019A3A0000B6BA070000000000E329000000000000180000000000000001000000000000001E0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000300000000000000F5018F029B3A0000B7BA070000000000E329000000000000190000000000000001000000000000001E00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000008F0229037D000000B8BA070000000000E329000000000000140000000000000005000000000000001E00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002903C303471F0000B9BA070000000000E329000000000000150000000000000003000000000000001E0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 C303 5D04 07340000 1FC0070000000000E329000000000000010000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000029000000000000005D04F704 8D480000 20C0070000000000E329000000000000030000000000000001000000000000000000000000000000000000000000000000000000000000000064E2040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002900000000000000 F704 9105 8E480000 	21C0070000000000E32900000000000004000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002900000000000000 9105 2B06 8F480000 23C0070000000000E32900000000000005000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002900000000000000 2B06 C506 B5010000 DEC1070000000000 E3290000000000001A000000000000000600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 C506 0000 B6010000 DFC1070000000000E3290000000000001B000000000000000600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

        public override void Write(BinaryWriter writer)
        {
            lock (Player.Inventory.ItemsLock)
            {
                WriteH(writer, (short) Player.Inventory.Items.Count);
                WriteH(writer, 0); //first item shift
                WriteUid(writer, Player);
                WriteQ(writer, Player.Inventory.Money);
                WriteC(writer, (byte) (IsInventory ? 1 : 0));
                WriteH(writer, 1); //Unk
                WriteD(writer, Player.Inventory.Size); //Size

                WriteD(writer, 34); //EU ???
                WriteD(writer, 34); //EU ???

                writer.Seek(6, SeekOrigin.Begin);
                WriteH(writer, (short) writer.BaseStream.Length);
                writer.Seek(0, SeekOrigin.End);

                int counter = 0;

                foreach (KeyValuePair<int, StorageItem> inventoryItem in Player.Inventory.Items)
                {
                    counter++;
                    short nowShift = (short) writer.BaseStream.Length;

                    WriteH(writer, nowShift); //Now shift
                    WriteH(writer, 0); //Next shift

                    WriteD(writer, inventoryItem.Value.ItemId);
                    WriteUid(writer, inventoryItem.Value);
                    WriteD(writer, 7441);
                    WriteD(writer, 0);
                    WriteD(writer, inventoryItem.Key);
                    WriteD(writer, 0);
                    WriteD(writer, inventoryItem.Value.Amount);
                    WriteD(writer, 0);
                    WriteD(writer, 0);
                    WriteD(writer, 1); //Binded?
                    WriteD(writer, 0);
                    WriteD(writer, 0);
                    WriteD(writer, 0);
                    WriteD(writer, 0);
                    WriteC(writer, 0);
                    WriteD(writer, 0); //EffectId
                    WriteD(writer, 0); //EffectId
                    WriteD(writer, 0); //EffectId
                    WriteD(writer, 0); //EffectId
                    WriteB(writer, new byte[65]); //unk
                    WriteD(writer, 1); //Item Level
                    WriteD(writer, 0);

                    if (counter < Player.Inventory.Items.Count)
                    {
                        writer.Seek(nowShift + 2, SeekOrigin.Begin);
                        WriteH(writer, (short) writer.BaseStream.Length);
                        writer.Seek(0, SeekOrigin.End);
                    }
                }
            }
        }
    }
}